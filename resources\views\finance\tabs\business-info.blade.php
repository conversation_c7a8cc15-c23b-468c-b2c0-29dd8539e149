<!-- Business Info Tab Content -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="mb-0">{{ __('Business Information') }}</h4>
        <p class="text-muted">{{ __('Manage your business details and settings') }}</p>
    </div>
</div>

<!-- Business Profile -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ __('Business Profile') }}</h5>
                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#editBusinessModal">
                    <i class="ti ti-edit me-1"></i>{{ __('Edit') }}
                </button>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center mb-3">
                        <div class="position-relative d-inline-block">
                            @if(\Auth::user()->avatar)
                                <img src="{{ \Auth::user()->profile }}" alt="Business Logo" class="img-fluid rounded" style="max-width: 150px; max-height: 150px;">
                            @else
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 150px; height: 150px;">
                                    <i class="ti ti-building text-muted" style="font-size: 3rem;"></i>
                                </div>
                            @endif
                            <button class="btn btn-sm btn-primary position-absolute bottom-0 end-0 rounded-circle" style="width: 30px; height: 30px;">
                                <i class="ti ti-camera" style="font-size: 0.8rem;"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-sm-6 mb-3">
                                <label class="form-label text-muted">{{ __('Business Name') }}</label>
                                <p class="mb-0 fw-semibold">{{ \Auth::user()->name ?? 'Your Business Name' }}</p>
                            </div>
                            <div class="col-sm-6 mb-3">
                                <label class="form-label text-muted">{{ __('Email') }}</label>
                                <p class="mb-0">{{ \Auth::user()->email ?? '<EMAIL>' }}</p>
                            </div>
                            <div class="col-sm-6 mb-3">
                                <label class="form-label text-muted">{{ __('Phone') }}</label>
                                <p class="mb-0">{{ \Auth::user()->phone ?? 'Not provided' }}</p>
                            </div>
                            <div class="col-sm-6 mb-3">
                                <label class="form-label text-muted">{{ __('Website') }}</label>
                                <p class="mb-0">{{ \Auth::user()->website ?? 'Not provided' }}</p>
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label text-muted">{{ __('Address') }}</label>
                                <p class="mb-0">{{ \Auth::user()->address ?? 'Address not provided' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ __('Quick Stats') }}</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <i class="ti ti-users text-primary me-2"></i>
                            <span>{{ __('Total Customers') }}</span>
                        </div>
                        <span class="badge bg-primary rounded-pill">{{ \App\Models\Customer::where('created_by', \Auth::user()->creatorId())->count() }}</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <i class="ti ti-file-invoice text-success me-2"></i>
                            <span>{{ __('Total Invoices') }}</span>
                        </div>
                        <span class="badge bg-success rounded-pill">{{ \App\Models\Invoice::where('created_by', \Auth::user()->creatorId())->count() }}</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <i class="ti ti-package text-info me-2"></i>
                            <span>{{ __('Total Products') }}</span>
                        </div>
                        <span class="badge bg-info rounded-pill">{{ \App\Models\ProductService::where('created_by', \Auth::user()->creatorId())->count() }}</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <i class="ti ti-receipt text-warning me-2"></i>
                            <span>{{ __('Total Expenses') }}</span>
                        </div>
                        <span class="badge bg-warning rounded-pill">{{ \App\Models\Expense::where('created_by', \Auth::user()->creatorId())->count() }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Business Settings -->
<div class="row mt-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ __('Financial Settings') }}</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="{{ route('taxes.index') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <i class="ti ti-receipt-tax me-2"></i>{{ __('Tax Settings') }}
                        </div>
                        <i class="ti ti-chevron-right"></i>
                    </a>
                    <a href="{{ route('bank-account.index') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <i class="ti ti-building-bank me-2"></i>{{ __('Bank Accounts') }}
                        </div>
                        <i class="ti ti-chevron-right"></i>
                    </a>
                    <a href="{{ route('settings') }}#payment-settings" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <i class="ti ti-credit-card me-2"></i>{{ __('Payment Methods') }}
                        </div>
                        <i class="ti ti-chevron-right"></i>
                    </a>
                    <a href="{{ route('settings') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <i class="ti ti-currency-dollar me-2"></i>{{ __('Currency Settings') }}
                        </div>
                        <i class="ti ti-chevron-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ __('System Settings') }}</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="{{ route('users.index') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <i class="ti ti-users me-2"></i>{{ __('User Management') }}
                        </div>
                        <i class="ti ti-chevron-right"></i>
                    </a>
                    <a href="{{ route('roles.index') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <i class="ti ti-shield me-2"></i>{{ __('Roles & Permissions') }}
                        </div>
                        <i class="ti ti-chevron-right"></i>
                    </a>
                    <a href="{{ route('print.setting') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <i class="ti ti-printer me-2"></i>{{ __('Print Settings') }}
                        </div>
                        <i class="ti ti-chevron-right"></i>
                    </a>
                    <a href="{{ route('email_template.index') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <i class="ti ti-mail me-2"></i>{{ __('Email Templates') }}
                        </div>
                        <i class="ti ti-chevron-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Business Documents -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ __('Business Documents') }}</h5>
                <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#uploadDocumentModal">
                    <i class="ti ti-upload me-1"></i>{{ __('Upload Document') }}
                </button>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="ti ti-file-certificate text-primary" style="font-size: 2rem;"></i>
                                <h6 class="mt-2 mb-1">{{ __('Business License') }}</h6>
                                <small class="text-muted">{{ __('Not uploaded') }}</small>
                                <div class="mt-2">
                                    <button class="btn btn-sm btn-outline-primary">
                                        <i class="ti ti-upload"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="ti ti-receipt-tax text-success" style="font-size: 2rem;"></i>
                                <h6 class="mt-2 mb-1">{{ __('Tax Certificate') }}</h6>
                                <small class="text-muted">{{ __('Not uploaded') }}</small>
                                <div class="mt-2">
                                    <button class="btn btn-sm btn-outline-success">
                                        <i class="ti ti-upload"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="ti ti-building-bank text-info" style="font-size: 2rem;"></i>
                                <h6 class="mt-2 mb-1">{{ __('Bank Statement') }}</h6>
                                <small class="text-muted">{{ __('Not uploaded') }}</small>
                                <div class="mt-2">
                                    <button class="btn btn-sm btn-outline-info">
                                        <i class="ti ti-upload"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="ti ti-file-text text-warning" style="font-size: 2rem;"></i>
                                <h6 class="mt-2 mb-1">{{ __('Other Documents') }}</h6>
                                <small class="text-muted">{{ __('Not uploaded') }}</small>
                                <div class="mt-2">
                                    <button class="btn btn-sm btn-outline-warning">
                                        <i class="ti ti-upload"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Business Modal -->
<div class="modal fade" id="editBusinessModal" tabindex="-1" aria-labelledby="editBusinessModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editBusinessModalLabel">{{ __('Edit Business Information') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="businessName" class="form-label">{{ __('Business Name') }}</label>
                                <input type="text" class="form-control" id="businessName" value="{{ \Auth::user()->name }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="businessEmail" class="form-label">{{ __('Email') }}</label>
                                <input type="email" class="form-control" id="businessEmail" value="{{ \Auth::user()->email }}">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="businessPhone" class="form-label">{{ __('Phone') }}</label>
                                <input type="tel" class="form-control" id="businessPhone" value="{{ \Auth::user()->phone }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="businessWebsite" class="form-label">{{ __('Website') }}</label>
                                <input type="url" class="form-control" id="businessWebsite" value="{{ \Auth::user()->website }}">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="businessAddress" class="form-label">{{ __('Address') }}</label>
                        <textarea class="form-control" id="businessAddress" rows="3">{{ \Auth::user()->address }}</textarea>
                    </div>
                    <div class="mb-3">
                        <label for="businessLogo" class="form-label">{{ __('Business Logo') }}</label>
                        <input type="file" class="form-control" id="businessLogo" accept="image/*">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                <button type="button" class="btn btn-primary">{{ __('Save Changes') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Upload Document Modal -->
<div class="modal fade" id="uploadDocumentModal" tabindex="-1" aria-labelledby="uploadDocumentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadDocumentModalLabel">{{ __('Upload Document') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="documentType" class="form-label">{{ __('Document Type') }}</label>
                        <select class="form-select" id="documentType">
                            <option value="">{{ __('Select Document Type') }}</option>
                            <option value="license">{{ __('Business License') }}</option>
                            <option value="tax">{{ __('Tax Certificate') }}</option>
                            <option value="bank">{{ __('Bank Statement') }}</option>
                            <option value="other">{{ __('Other') }}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="documentFile" class="form-label">{{ __('Choose File') }}</label>
                        <input type="file" class="form-control" id="documentFile" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                    </div>
                    <div class="mb-3">
                        <label for="documentDescription" class="form-label">{{ __('Description') }}</label>
                        <textarea class="form-control" id="documentDescription" rows="3" placeholder="{{ __('Document description') }}"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                <button type="button" class="btn btn-success">{{ __('Upload') }}</button>
            </div>
        </div>
    </div>
</div>
