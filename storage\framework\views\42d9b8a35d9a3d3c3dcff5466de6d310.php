    <?php
        use App\Models\Utility;
        $setting = \App\Models\Utility::settings();
        $logo = \App\Models\Utility::get_file('uploads/logo');

        $company_logo = $setting['company_logo_dark'] ?? '';
        $company_logos = $setting['company_logo_light'] ?? '';
        $company_small_logo = $setting['company_small_logo'] ?? '';

        $emailTemplate = \App\Models\EmailTemplate::emailTemplateData();
        $lang = Auth::user()->lang;

        $user = \Auth::user();
        $userPlan = null;
        if ($user->type === 'company') {
            // Load the pricing plan relationship or find by ID if relationship fails
            $pricingPlan = $user->plan;
            if (is_numeric($pricingPlan)) {
                // If plan returns an ID instead of model, fetch the model
                $pricingPlan = \App\Models\PricingPlan::find($pricingPlan);
            }
            $userPlan = $pricingPlan;
        } elseif ($user->type === 'employee') {
            // For employees, get the company's plan to check module availability
            $companyUser = \App\Models\User::find($user->created_by);
            if ($companyUser && $companyUser->plan) {
                $pricingPlan = $companyUser->plan;
                if (is_numeric($pricingPlan)) {
                    $pricingPlan = \App\Models\PricingPlan::find($pricingPlan);
                }
                $userPlan = $pricingPlan;
            }
        } else {
            $userPlan = \App\Models\Plan::getPlan($user->show_dashboard());
        }
    ?>

<?php if(isset($setting['cust_theme_bg']) && $setting['cust_theme_bg'] == 'on'): ?>
    <nav class="dash-sidebar light-sidebar transprent-bg">
    <?php else: ?>
        <nav class="dash-sidebar light-sidebar ">
<?php endif; ?>
<div class="navbar-wrapper">
    <div class="m-header main-logo">
        <a href="#" class="b-brand">

            <?php if($setting['cust_darklayout'] && $setting['cust_darklayout'] == 'on'): ?>
                <img src="<?php echo e($logo . '/' . (isset($company_logos) && !empty($company_logos) ? $company_logos : 'logo-light.png') . '?' . time()); ?>"
                    alt="<?php echo e(config('app.name', 'ERPGo-SaaS')); ?>" class="logo logo-lg">
            <?php else: ?>
                <img src="<?php echo e($logo . '/' . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png') . '?' . time()); ?>"
                    alt="<?php echo e(config('app.name', 'ERPGo-SaaS')); ?>" class="logo logo-lg">
            <?php endif; ?>

        </a>

    </div>
    <div class="navbar-content">
        <?php if(\Auth::user()->type != 'client'): ?>
            <ul class="dash-navbar">
                <?php if(Request::segment(1) != 'settings'): ?>
                <!--------------------- Start Dashboard ----------------------------------->
                <?php if(Gate::check('show hrm dashboard') ||
                        Gate::check('show project dashboard') ||
                        Gate::check('show account dashboard') ||
                        Gate::check('show crm dashboard') ||
                        Gate::check('show pos dashboard')): ?>
                    <li
                        class="dash-item dash-hasmenu
                                <?php echo e(Request::segment(1) == null ||
                                Request::segment(1) == 'account-dashboard' ||
                                Request::segment(1) == 'hrm-dashboard' ||
                                Request::segment(1) == 'crm-dashboard' ||
                                Request::segment(1) == 'project-dashboard' ||
                                Request::segment(1) == 'account-statement-report' ||
                                Request::segment(1) == 'invoice-summary' ||
                                Request::segment(1) == 'sales' ||
                                Request::segment(1) == 'receivables' ||
                                Request::segment(1) == 'payables' ||
                                Request::segment(1) == 'bill-summary' ||
                                Request::segment(1) == 'product-stock-report' ||
                                Request::segment(1) == 'transaction' ||
                                Request::segment(1) == 'income-summary' ||
                                Request::segment(1) == 'expense-summary' ||
                                Request::segment(1) == 'income-vs-expense-summary' ||
                                Request::segment(1) == 'tax-summary' ||
                                Request::segment(1) == 'income report' ||
                                Request::segment(1) == 'report' ||
                                Request::segment(1) == 'reports-monthly-cashflow' ||
                                Request::segment(1) == 'reports-quarterly-cashflow' ||
                                Request::segment(1) == 'reports-payroll' ||
                                Request::segment(1) == 'report-leave' ||
                                Request::segment(1) == 'reports-monthly-attendance' ||
                                Request::segment(1) == 'reports-lead' ||
                                Request::segment(1) == 'reports-deal' ||
                                Request::segment(1) == 'pos-dashboard' ||
                                Request::segment(1) == 'reports-warehouse' ||
                                Request::segment(1) == 'reports-daily-purchase' ||
                                Request::segment(1) == 'reports-monthly-purchase' ||
                                Request::segment(1) == 'reports-daily-pos' ||
                                Request::segment(1) == 'reports-monthly-pos' ||
                                Request::segment(1) == 'reports-pos-vs-purchase'
                                    ? 'active dash-trigger'
                                    : ''); ?>">
                        <a href="<?php echo e(route('dashboard')); ?>" class="dash-link ">
                            <span class="dash-micon">
                                <i class="fas fa-home"></i>
                            </span>
                            <span class="dash-mtext"><?php echo e(__('Dashboard')); ?></span>
                            <span class="dash-arrow"></span></a>
                        <ul class="dash-submenu">
                            <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('account')) || ($user->type === 'employee' && $userPlan->hasModule('account')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->account == 1)) && Gate::check('show account dashboard')): ?>
                                <!-- <li
                                    class="dash-item <?php echo e(Request::segment(1) == null || Request::segment(1) == 'account-dashboard' ? ' active' : ''); ?>">
                                    <a class="dash-link"
                                        href="<?php echo e(route('dashboard')); ?>"><?php echo e(__(' Overview')); ?>

                                    </a>
                                </li> -->
                            <?php endif; ?>
                            <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('crm')) || ($user->type === 'employee' && $userPlan->hasModule('crm')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->crm == 1))): ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('show crm dashboard')): ?>
                                    <!-- <li
                                        class="dash-item <?php echo e(Request::segment(1) == 'crm-dashboard' ? ' active' : ''); ?>">
                                        <a class="dash-link" href="#"><?php echo e(__('CRM')); ?></a>
                                    </li> -->
                                <?php endif; ?>
                            <?php endif; ?>
                        </ul>
                    </li>
                <?php endif; ?>
                <!--------------------- End Dashboard ----------------------------------->

                <!--------------------- Start Reports | Analytics ----------------------------------->
                <?php if(
                    (!empty($userPlan) && (
                        ($user->type === 'company' && ($userPlan->hasModule('account') || $userPlan->hasModule('crm')))
                        || ($user->type === 'employee' && ($userPlan->hasModule('account') || $userPlan->hasModule('crm')))
                        || ($user->type !== 'company' && $user->type !== 'employee' && ($userPlan->account == 1 || $userPlan->crm == 1))
                    ))
                ): ?>
                    <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'account-report' || Request::segment(1) == 'crm-report' ? 'active dash-trigger' : ''); ?>">
                        <a href="#" class="dash-link">
                            <span class="dash-micon"><i class="fas fa-chart-line fa-2"></i></span>
                            <span class="dash-mtext"><?php echo e(__('Reports | Analytics')); ?></span>
                            <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                        </a>
                        <ul class="dash-submenu">
                            <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('account')) || ($user->type === 'employee' && $userPlan->hasModule('account')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->account == 1))): ?>
                                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'account-report' || Request::segment(1) == 'report' || Request::segment(1) == 'reports-monthly-cashflow' || Request::segment(1) == 'reports-quarterly-cashflow' ? 'active dash-trigger' : ''); ?>">
                                    <a class="dash-link" href="#"><?php echo e(__('Accounts')); ?><span class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                    <ul class="dash-submenu">
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('statement report')): ?>
                                            <li class="dash-item <?php echo e(Request::route()->getName() == 'report.account.statement' ? ' active' : ''); ?>">
                                                <a class="dash-link" href="<?php echo e(route('report.account.statement')); ?>"><?php echo e(__('Account Statement')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('invoice report')): ?>
                                            <li class="dash-item <?php echo e(Request::route()->getName() == 'report.invoice.summary' ? ' active' : ''); ?>">
                                                <a class="dash-link" href="<?php echo e(route('report.invoice.summary')); ?>"><?php echo e(__('Invoice Summary')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <li class="dash-item <?php echo e(Request::route()->getName() == 'report.sales' ? ' active' : ''); ?>">
                                            <a class="dash-link" href="<?php echo e(route('report.sales')); ?>"><?php echo e(__('Sales Report')); ?></a>
                                        </li>
                                        <li class="dash-item <?php echo e(Request::route()->getName() == 'report.receivables' ? ' active' : ''); ?>">
                                            <a class="dash-link" href="<?php echo e(route('report.receivables')); ?>"><?php echo e(__('Receivables')); ?></a>
                                        </li>
                                        <li class="dash-item <?php echo e(Request::route()->getName() == 'report.payables' ? ' active' : ''); ?>">
                                            <a class="dash-link" href="<?php echo e(route('report.payables')); ?>"><?php echo e(__('Payables')); ?></a>
                                        </li>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('bill report')): ?>
                                            <li class="dash-item <?php echo e(Request::route()->getName() == 'report.bill.summary' ? ' active' : ''); ?>">
                                                <a class="dash-link" href="<?php echo e(route('report.bill.summary')); ?>"><?php echo e(__('Bill Summary')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('stock report')): ?>
                                            <li class="dash-item <?php echo e(Request::route()->getName() == 'report.product.stock.report' ? ' active' : ''); ?>">
                                                <a href="<?php echo e(route('report.product.stock.report')); ?>" class="dash-link"><?php echo e(__('Product Stock')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('loss & profit report')): ?>
                                            <li class="dash-item <?php echo e(request()->is('reports-monthly-cashflow') || request()->is('reports-quarterly-cashflow') ? 'active' : ''); ?>">
                                                <a class="dash-link" href="<?php echo e(route('report.monthly.cashflow')); ?>"><?php echo e(__('Cash Flow')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage transaction')): ?>
                                            <li class="dash-item <?php echo e(Request::route()->getName() == 'transaction.index' || Request::route()->getName() == 'transfer.create' || Request::route()->getName() == 'transaction.edit' ? ' active' : ''); ?>">
                                                <a class="dash-link" href="<?php echo e(route('transaction.index')); ?>"><?php echo e(__('Transaction')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('income report')): ?>
                                            <li class="dash-item <?php echo e(Request::route()->getName() == 'report.income.summary' ? ' active' : ''); ?>">
                                                <a class="dash-link" href="<?php echo e(route('report.income.summary')); ?>"><?php echo e(__('Income Summary')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('expense report')): ?>
                                            <li class="dash-item <?php echo e(Request::route()->getName() == 'report.expense.summary' ? ' active' : ''); ?>">
                                                <a class="dash-link" href="<?php echo e(route('report.expense.summary')); ?>"><?php echo e(__('Expense Summary')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('income vs expense report')): ?>
                                            <li class="dash-item <?php echo e(Request::route()->getName() == 'report.income.vs.expense.summary' ? ' active' : ''); ?>">
                                                <a class="dash-link" href="<?php echo e(route('report.income.vs.expense.summary')); ?>"><?php echo e(__('Income VS Expense')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('tax report')): ?>
                                            <li class="dash-item <?php echo e(Request::route()->getName() == 'report.tax.summary' ? ' active' : ''); ?>">
                                                <a class="dash-link" href="<?php echo e(route('report.tax.summary')); ?>"><?php echo e(__('Tax Summary')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </li>
                            <?php endif; ?>
                            <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('crm')) || ($user->type === 'employee' && $userPlan->hasModule('crm')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->crm == 1))): ?>
                                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'crm-report' || Request::segment(1) == 'reports-lead' || Request::segment(1) == 'reports-deal' ? 'active dash-trigger' : ''); ?>">
                                    <a class="dash-link" href="#"><?php echo e(__('CRM')); ?><span class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                    <ul class="dash-submenu">
                                        <li class="dash-item <?php echo e(request()->is('reports-lead') ? 'active' : ''); ?>">
                                            <a class="dash-link" href="<?php echo e(route('report.lead')); ?>"><?php echo e(__('Lead')); ?></a>
                                        </li>
                                        <li class="dash-item <?php echo e(request()->is('reports-deal') ? 'active' : ''); ?>">
                                            <a class="dash-link" href="<?php echo e(route('report.deal')); ?>"><?php echo e(__('Deal')); ?></a>
                                        </li>
                                    </ul>
                                </li>
                            <?php endif; ?>
                            <!-- Projects Menu Section -->
                            <li class="dash-item dash-hasmenu <?php echo e(in_array(Request::segment(1), ['team-performance', 'task-analysis', 'performance-analysis']) ? 'active dash-trigger' : ''); ?>">
                                <a class="dash-link" href="#">
                                    <span class="dash-mtext"><?php echo e(__('Projects')); ?></span>
                                    <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                                </a>
                                <ul class="dash-submenu">
                                    <li class="dash-item <?php echo e(Request::segment(1) == 'team-performance' ? 'active' : ''); ?>">
                                        <a class="dash-link" href="<?php echo e(route('projects.team-performance')); ?>"><?php echo e(__('Team Performance')); ?></a>
                                    </li>
                                    <li class="dash-item <?php echo e(Request::segment(1) == 'task-analysis' ? 'active' : ''); ?>">
                                        <a class="dash-link" href="<?php echo e(route('projects.task-analysis')); ?>"><?php echo e(__('Task Analysis')); ?></a>
                                    </li>
                                    <li class="dash-item <?php echo e(Request::segment(1) == 'performance-analysis' ? 'active' : ''); ?>">
                                        <a class="dash-link" href="<?php echo e(route('projects.performance-analysis')); ?>"><?php echo e(__('Performance Analysis')); ?></a>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </li>
                <?php endif; ?>
                <!--------------------- End Reports | Analytics ----------------------------------->

                <!--------------------- Start HRM ----------------------------------->

                
                
                                            

                <!--------------------- End HRM ----------------------------------->

                <!--------------------- Start Account ----------------------------------->

                <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('account')) || ($user->type === 'employee' && $userPlan->hasModule('account')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->account == 1))): ?>
                    <?php if(Gate::check('manage budget plan') || Gate::check('income vs expense report') ||
                            Gate::check('manage goal') || Gate::check('manage constant tax') ||
                            Gate::check('manage constant category') || Gate::check('manage constant unit') ||
                            Gate::check('manage constant custom field') || Gate::check('manage print settings') ||
                            Gate::check('manage customer') || Gate::check('manage vender') ||
                            Gate::check('manage proposal') || Gate::check('manage bank account') ||
                            Gate::check('manage bank transfer') || Gate::check('manage invoice') ||
                            Gate::check('manage revenue') || Gate::check('manage credit note') ||
                            Gate::check('manage bill') || Gate::check('manage payment') ||
                            Gate::check('manage debit note') || Gate::check('manage chart of account') ||
                            Gate::check('manage journal entry') || Gate::check('balance sheet report') ||
                            Gate::check('ledger report') || Gate::check('trial balance report') ): ?>
                        <li
                            class="dash-item dash-hasmenu
                                        <?php echo e(Request::route()->getName() == 'print-setting' ||
                                        Request::segment(1) == 'customer' || Request::segment(1) == 'vender' ||
                                        Request::segment(1) == 'proposal' || Request::segment(1) == 'bank-account' ||
                                        Request::segment(1) == 'bank-transfer' || Request::segment(1) == 'invoice' ||
                                        Request::segment(1) == 'revenue' || Request::segment(1) == 'credit-note' ||
                                        Request::segment(1) == 'taxes' || Request::segment(1) == 'product-category' ||
                                        Request::segment(1) == 'product-unit' || Request::segment(1) == 'payment-method' ||
                                        Request::segment(1) == 'custom-field' || Request::segment(1) == 'chart-of-account-type' ||
                                        (Request::segment(1) == 'transaction' && Request::segment(2) != 'ledger' &&
                                            Request::segment(2) != 'balance-sheet-report' && Request::segment(2) != 'trial-balance') ||
                                        Request::segment(1) == 'goal' || Request::segment(1) == 'budget' ||
                                        Request::segment(1) == 'chart-of-account' || Request::segment(1) == 'journal-entry' ||
                                        Request::segment(2) == 'ledger' || Request::segment(2) == 'balance-sheet' ||
                                        Request::segment(2) == 'trial-balance' || Request::segment(2) == 'profit-loss' ||
                                        Request::segment(1) == 'bill' || Request::segment(1) == 'expense' ||
                                        Request::segment(1) == 'payment' || Request::segment(1) == 'debit-note' || (Request::route()->getName() == 'report.balance.sheet') || (Request::route()->getName() == 'trial-balance-report') ? ' active dash-trigger'
                                            : ''); ?>">
                            <a href="#!" class="dash-link"><span class="dash-micon"><i
                                        class="fas fa-business-time fa-2"></i></span><span
                                    class="dash-mtext"><?php echo e(__('Accounting System ')); ?>

                                </span><span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="dash-submenu">

                                <?php if(Gate::check('manage bank account') || Gate::check('manage bank transfer')): ?>
                                    <li
                                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'bank-account' || Request::segment(1) == 'bank-transfer' ? 'active dash-trigger' : ''); ?>">
                                        <a class="dash-link" href="#"><?php echo e(__('Banking')); ?><span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            <li
                                                class="dash-item <?php echo e(Request::route()->getName() == 'bank-account.index' || Request::route()->getName() == 'bank-account.create' || Request::route()->getName() == 'bank-account.edit' ? ' active' : ''); ?>">
                                                <a class="dash-link"
                                                    href="<?php echo e(route('bank-account.index')); ?>"><?php echo e(__('Account')); ?></a>
                                            </li>
                                            <li
                                                class="dash-item <?php echo e(Request::route()->getName() == 'bank-transfer.index' || Request::route()->getName() == 'bank-transfer.create' || Request::route()->getName() == 'bank-transfer.edit' ? ' active' : ''); ?>">
                                                <a class="dash-link"
                                                    href="<?php echo e(route('bank-transfer.index')); ?>"><?php echo e(__('Transfer')); ?></a>
                                            </li>
                                        </ul>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage customer') ||
                                        Gate::check('manage proposal') ||
                                        Gate::check('manage invoice') ||
                                        Gate::check('manage revenue') ||
                                        Gate::check('manage credit note')): ?>
                                    <li
                                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'customer' || Request::segment(1) == 'proposal' || Request::segment(1) == 'invoice' || Request::segment(1) == 'revenue' || Request::segment(1) == 'credit-note' ? 'active dash-trigger' : ''); ?>">
                                        <a class="dash-link" href="#"><?php echo e(__('Sales')); ?><span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            <?php if(Gate::check('manage customer')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::segment(1) == 'customer' ? 'active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('customer.index')); ?>"><?php echo e(__('Customer')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if(Gate::check('manage proposal')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::segment(1) == 'proposal' ? 'active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('proposal.index')); ?>"><?php echo e(__('Estimate')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage invoice')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'invoice.index' || Request::route()->getName() == 'invoice.create' || Request::route()->getName() == 'invoice.edit' || Request::route()->getName() == 'invoice.show' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('invoice.index')); ?>"><?php echo e(__('Invoice')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage revenue')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'revenue.index' || Request::route()->getName() == 'revenue.create' || Request::route()->getName() == 'revenue.edit' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('revenue.index')); ?>"><?php echo e(__('Revenue')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage credit note')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'credit.note' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('credit.note')); ?>"><?php echo e(__('Credit Note')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage vender') ||
                                        Gate::check('manage bill') ||
                                        Gate::check('manage payment') ||
                                        Gate::check('manage debit note')): ?>
                                    <li
                                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'bill' || Request::segment(1) == 'vender' || Request::segment(1) == 'expense' || Request::segment(1) == 'payment' || Request::segment(1) == 'debit-note' ? 'active dash-trigger' : ''); ?>">
                                        <a class="dash-link" href="#"><?php echo e(__('Purchases')); ?><span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            <?php if(Gate::check('manage vender')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::segment(1) == 'vender' ? 'active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('vender.index')); ?>"><?php echo e(__('Suppiler')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage bill')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'bill.index' || Request::route()->getName() == 'bill.create' || Request::route()->getName() == 'bill.edit' || Request::route()->getName() == 'bill.show' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                    href="<?php echo e(route('bill.index')); ?>"><?php echo e(__('Bill')); ?></a>
                                                </li>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'expense.index' || Request::route()->getName() == 'expense.create' || Request::route()->getName() == 'expense.edit' || Request::route()->getName() == 'expense.show' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('expense.index')); ?>"><?php echo e(__('Expense')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage payment')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'payment.index' || Request::route()->getName() == 'payment.create' || Request::route()->getName() == 'payment.edit' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('payment.index')); ?>"><?php echo e(__('Payment')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage debit note')): ?>
                                                <li
                                                    class="dash-item  <?php echo e(Request::route()->getName() == 'debit.note' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('debit.note')); ?>"><?php echo e(__('Debit Note')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage chart of account') ||
                                        Gate::check('manage journal entry') ||
                                        Gate::check('ledger report') ||
                                        Gate::check('bill report') ||
                                        Gate::check('income vs expense report') ||
                                        Gate::check('trial balance report')): ?>
                                    <li
                                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'chart-of-account' ||
                                        Request::segment(1) == 'journal-entry' ||
                                        Request::segment(2) == 'profit-loss' ||
                                        Request::segment(2) == 'ledger' ||
                                        Request::segment(2) == 'trial-balance-report' ||
                                        Request::segment(2) == 'balance-sheet-report' ||
                                        Request::segment(2) == 'trial-balance' || (Request::route()->getName() == 'report.balance.sheet') || (Request::route()->getName() == 'trial-balance-report') ? 'active dash-trigger'
                                            : ''); ?>">
                                        <a class="dash-link" href="#"><?php echo e(__('Double Entry')); ?><span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage chart of account')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'chart-of-account.index' || Request::route()->getName() == 'chart-of-account.show' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('chart-of-account.index')); ?>"><?php echo e(__('Chart of Accounts')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage journal entry')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'journal-entry.edit' ||
                                                    Request::route()->getName() == 'journal-entry.create' ||
                                                    Request::route()->getName() == 'journal-entry.index' ||
                                                    Request::route()->getName() == 'journal-entry.show'
                                                        ? ' active'
                                                        : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('journal-entry.index')); ?>"><?php echo e(__('Journal Account')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('ledger report')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'report.ledger' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('report.ledger', 0)); ?>"><?php echo e(__('Ledger Summary')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('bill report')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'report.balance.sheet' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('report.balance.sheet')); ?>"><?php echo e(__('Balance Sheet')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('income vs expense report')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'report.profit.loss' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('report.profit.loss')); ?>"><?php echo e(__('Profit & Loss')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('trial balance report')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'trial.balance' || (Request::route()->getName() == 'trial-balance-report') ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('trial.balance')); ?>"><?php echo e(__('Trial Balance')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </li>
                                <?php endif; ?>
                                <?php if(\Auth::user()->type == 'company'): ?>
                                    <li class="dash-item <?php echo e(Request::segment(1) == 'budget' ? 'active' : ''); ?>">
                                        <a class="dash-link"
                                            href="<?php echo e(route('budget.index')); ?>"><?php echo e(__('Budget Planner')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage goal')): ?>
                                    <li class="dash-item <?php echo e(Request::segment(1) == 'goal' ? 'active' : ''); ?>">
                                        <a class="dash-link"
                                            href="<?php echo e(route('goal.index')); ?>"><?php echo e(__('Financial Goal')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage constant tax') ||
                                        Gate::check('manage constant category') ||
                                        Gate::check('manage constant unit') ||
                                        Gate::check('manage constant custom field')): ?>
                                    <li
                                        class="dash-item <?php echo e(Request::segment(1) == 'taxes' || Request::segment(1) == 'product-category' || Request::segment(1) == 'product-unit' || Request::segment(1) == 'payment-method' || Request::segment(1) == 'custom-field' || Request::segment(1) == 'chart-of-account-type' ? 'active dash-trigger' : ''); ?>">
                                        <a class="dash-link"
                                            href="<?php echo e(route('taxes.index')); ?>"><?php echo e(__('Accounting Setup')); ?></a>
                                    </li>
                                <?php endif; ?>

                                <?php if(Gate::check('manage print settings')): ?>
                                    <li
                                        class="dash-item <?php echo e(Request::route()->getName() == 'print-setting' ? ' active' : ''); ?>">
                                        <a class="dash-link"
                                            href="<?php echo e(route('print.setting')); ?>"><?php echo e(__('Print Settings')); ?></a>
                                    </li>
                                <?php endif; ?>

                            </ul>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!--------------------- End Account ----------------------------------->

                <!--------------------- Start Finance ----------------------------------->

                <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('account')) || ($user->type === 'employee' && $userPlan->hasModule('account')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->account == 1))): ?>
                    <?php if(Gate::check('manage customer') || Gate::check('manage vender') || Gate::check('manage proposal') ||
                         Gate::check('manage invoice') || Gate::check('manage revenue') || Gate::check('manage bill') ||
                         Gate::check('manage expense') || Gate::check('manage payment') || Gate::check('manage chart of account') ||
                         Gate::check('manage journal entry') || Gate::check('ledger report') || Gate::check('bill report') ||
                         Gate::check('manage bank account') || Gate::check('manage bank transfer')): ?>
                        <li class="dash-item <?php echo e(Request::segment(1) == 'finance' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('finance.dashboard')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-chart-line"></i></span>
                                <span class="dash-mtext"><?php echo e(__('Finance')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!--------------------- End Finance ----------------------------------->

                <!--------------------- Start CRM ----------------------------------->

                <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('crm')) || ($user->type === 'employee' && $userPlan->hasModule('crm')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->crm == 1))): ?>
                    <?php if(Gate::check('manage lead') ||
                            Gate::check('manage deal') ||
                            Gate::check('manage form builder') ||
                            Gate::check('manage contract') ||
                            Gate::check('manage pipeline') ||
                            true): ?>
                        <li
                            class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'stages' || Request::segment(1) == 'labels' || Request::segment(1) == 'sources' || Request::segment(1) == 'lead_stages' || Request::segment(1) == 'pipelines' || Request::segment(1) == 'deals' || Request::segment(1) == 'leads' || Request::segment(1) == 'form_builder' || Request::segment(1) == 'contractType' || Request::segment(1) == 'form_response' || Request::segment(1) == 'contract' ? ' active dash-trigger' : ''); ?>">
                            <a href="#!" class="dash-link"><span class="dash-micon"><i
                                        class="fas fa-qrcode fa-2"></i></span><span
                                    class="dash-mtext"><?php echo e(__('Lead Tracker')); ?></span><span class="dash-arrow"><i
                                        data-feather="chevron-right"></i></span></a>
                            <ul
                                class="dash-submenu <?php echo e(Request::segment(1) == 'stages' || Request::segment(1) == 'labels' || Request::segment(1) == 'sources' || Request::segment(1) == 'lead_stages' || Request::segment(1) == 'leads' || Request::segment(1) == 'form_builder' || Request::segment(1) == 'form_response' || Request::segment(1) == 'deals' || Request::segment(1) == 'pipelines' ? 'show' : ''); ?>">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage lead')): ?>
                                    <li
                                        class="dash-item <?php echo e(Request::route()->getName() == 'leads.list' || Request::route()->getName() == 'leads.index' || Request::route()->getName() == 'leads.show' ? ' active' : ''); ?>">
                                        <a class="dash-link" href="<?php echo e(route('leads.index')); ?>"><?php echo e(__('Leads')); ?></a>
                                    </li>
                                <?php endif; ?>
                                
                                <li
                                    class="dash-item <?php echo e(Request::route()->getName() == 'contacts.list' || Request::route()->getName() == 'contacts.index' || Request::route()->getName() == 'contacts.show' ? ' active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('contacts.index')); ?>"><?php echo e(__('Contacts')); ?></a>
                                </li>
                                <li
                                    class="dash-item <?php echo e(Request::route()->getName() == 'contact-groups.index' || Request::route()->getName() == 'contact-groups.show' ? ' active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('contact-groups.index')); ?>"><?php echo e(__('Contact Groups')); ?></a>
                                </li>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage form builder')): ?>
                                    <li
                                        class="dash-item <?php echo e(Request::segment(1) == 'form_builder' || Request::segment(1) == 'form_response' ? 'active open' : ''); ?>">
                                        <a class="dash-link"
                                            href="<?php echo e(route('form_builder.index')); ?>"><?php echo e(__('Form Builder')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <!-- <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage contract')): ?>
                                    <li
                                        class="dash-item  <?php echo e(Request::route()->getName() == 'contract.index' || Request::route()->getName() == 'contract.show' ? 'active' : ''); ?>">
                                        <a class="dash-link"
                                            href="<?php echo e(route('contract.index')); ?>"><?php echo e(__('Contract')); ?></a>
                                    </li>
                                <?php endif; ?> -->
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage pipeline')): ?>
                                    <li
                                        class="dash-item <?php echo e(Request::route()->getName() == 'pipeline.index' || Request::route()->getName() == 'pipelines.create' || Request::route()->getName() == 'pipelines.edit' ? 'active' : ''); ?>">
                                        <a class="dash-link"
                                            href="<?php echo e(route('pipeline.index')); ?>"><?php echo e(__('Pipelines')); ?></a>
                                    </li>
                                <?php endif; ?>
                        <?php if(Gate::check('manage lead stage') ||
                                Gate::check('manage pipeline') ||
                                Gate::check('manage source') ||
                                Gate::check('manage label') ||
                                Gate::check('manage contract type') ||
                                Gate::check('manage stage')): ?>
                            <!-- <li
                                class="dash-item  <?php echo e(Request::segment(1) == 'stages' || Request::segment(1) == 'labels' || Request::segment(1) == 'sources' || Request::segment(1) == 'lead_stages' || Request::segment(1) == 'pipelines' || Request::segment(1) == 'product-category' || Request::segment(1) == 'product-unit' || Request::segment(1) == 'contractType' || Request::segment(1) == 'payment-method' || Request::segment(1) == 'custom-field' || Request::segment(1) == 'chart-of-account-type' ? 'active dash-trigger' : ''); ?>">
                                <a class="dash-link"
                                    href="<?php echo e(route('pipelines.index')); ?>   "><?php echo e(__('CRM Settings')); ?></a>

                            </li> -->
                        <?php endif; ?>
                            </ul>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!--------------------- End CRM ----------------------------------->

                <!--------------------- Start Booking Module ----------------------------------->

                <!-- <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('booking')) || ($user->type === 'employee' && $userPlan->hasModule('booking')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->booking == 1))): ?>
                    <?php if(Gate::check('manage booking') || Gate::check('view booking')): ?>
                        <li
                            class="dash-item <?php echo e(Request::segment(1) == 'bookings' || Request::segment(1) == 'appointment-bookings' || Request::segment(1) == 'appointments' || Request::segment(1) == 'calendar' && Request::segment(2) == 'events' ? ' active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('calendar.view')); ?>">
                                <span class="dash-micon"><i class="ti ti-calendar-event"></i></span>
                                <span class="dash-mtext"><?php echo e(__('Calendar')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>  -->

                <!--------------------- End Booking Module ----------------------------------->

                <!--------------------- Start Project ----------------------------------->

                <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('project')) || ($user->type === 'employee' && $userPlan->hasModule('project')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->project == 1))): ?>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage project')): ?>
                        <li class="dash-item <?php echo e(Request::segment(1) == 'project' || Request::segment(1) == 'projects' || request()->is('projects/*') ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('projects.index')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-project-diagram fa-2"></i></span>
                                <span class="dash-mtext"><?php echo e(__('Mission Control')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!--------------------- End Project ----------------------------------->

                <!--------------------- Start My Tasks (Personal Tasks) ----------------------------------->
                <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('personal_tasks')) || ($user->type === 'employee' && $userPlan->hasModule('personal_tasks')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->personal_tasks == 1))): ?>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage personal task')): ?>
                        <li class="dash-item <?php echo e(Request::segment(1) == 'my-tasks' || Request::segment(1) == 'personal-tasks' || request()->is('personal-taskboard*') ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('my-tasks.index')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-user-check fa-2"></i></span>
                                <span class="dash-mtext"><?php echo e(__('My Tasks')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>
                <!--------------------- End My Tasks (Personal Tasks) ----------------------------------->

                <!--------------------- Start Booking System ----------------------------------->
                <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('booking')) || ($user->type === 'employee' && $userPlan->hasModule('booking')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->booking == 1))): ?>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage project task')): ?>
                        <li class="dash-item <?php echo e(request()->is('calendar*') ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('task.calendar', ['all'])); ?>" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-address-card fa-2x"></i></span>
                                <span class="dash-mtext"><?php echo e(__('Smart Scheduler')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>
                <!--------------------- End Booking System ----------------------------------->



                <!--------------------- Start User Managaement System ----------------------------------->

                <?php if(\Auth::user()->type != 'super admin' && \Auth::user()->type != 'system admin' && Gate::check('manage client')): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'clients' ? ' active dash-trigger' : ''); ?>">

                        <a href="#!" class="dash-link "><span class="dash-micon"><i class="fa fa-users fa-2x"></i></span><span
                                class="dash-mtext"><?php echo e(__('User Management')); ?></span><span class="dash-arrow"><i
                                    data-feather="chevron-right"></i></span></a>
                        <ul class="dash-submenu">
                            <!-- <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage user')): ?>
                                <li class="dash-item <?php echo e(Request::route()->getName() == 'users.index' ? 'active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('users.index')); ?>"><?php echo e(__('Users')); ?></a>
                                </li>
                            <?php endif; ?> -->
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage employee')): ?>
                                <li class="dash-item <?php echo e(Request::route()->getName() == 'employee.index' ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('employee.index')); ?>" class="dash-link">
                                    
                                        <span class="dash-mtext"><?php echo e(__('Employee')); ?></span>
                                    </a>
                                </li>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage client')): ?>
                                <li
                                    class="dash-item <?php echo e(Request::route()->getName() == 'clients.index' || Request::segment(1) == 'clients' || Request::route()->getName() == 'clients.edit' ? ' active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('clients.index')); ?>"><?php echo e(__('Client')); ?></a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </li>
                <?php endif; ?>

                <!--------------------- End User Managaement System----------------------------------->


                <!--------------------- Start Products System ----------------------------------->

                <?php if(Gate::check('manage product & service')): ?>
                    <li class="dash-item dash-hasmenu">
                        <a href="#!" class="dash-link ">
                            <span class="dash-micon"><i class="fas fa-tags fa-2"></i></span><span
                                class="dash-mtext"><?php echo e(__('Products System')); ?></span><span class="dash-arrow">
                                <i data-feather="chevron-right"></i></span>
                        </a>
                        <ul class="dash-submenu">
                            <?php if(Gate::check('manage product & service')): ?>
                                <li class="dash-item <?php echo e(Request::segment(1) == 'productservice' ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('productservice.index')); ?>"
                                        class="dash-link"><?php echo e(__('Product & Services')); ?>

                                    </a>
                                </li>
                                <li class="dash-item <?php echo e(Request::segment(1) == 'productstock' ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('productstock.index')); ?>"
                                        class="dash-link"><?php echo e(__('Product Stock')); ?>

                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </li>
                <?php endif; ?>

                <!--------------------- End Products System ----------------------------------->


                <!--------------------- Start POs System ----------------------------------->
                
                
                <!--------------------- End POs System ----------------------------------->

                <?php if(\Auth::user()->type != 'super admin'  && \Auth::user()->type != 'system admin' && \Auth::user()->type != 'staff'): ?>
                    <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'support' ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('support.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="fas fa-headset fa-2"></i></span><span
                                class="dash-mtext"><?php echo e(__('Support System')); ?></span>
                        </a>
                    </li>
                    
                    
                    <?php if(\Auth::user()->type != 'super admin' && \Auth::user()->type != 'system admin' && \Auth::user()->type != 'staff'): ?>
                        <!-- OMX Flow Module - Show for users with omx flow module permission -->
                        

                        <!-- WhatsApp Flows - Show only if user has specific permission -->
                        <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('omx_flow')) || ($user->type === 'employee' && $userPlan->hasModule('omx_flow')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->omx_flow == 1))): ?>
                            <?php if($user->hasModulePermission('omx_flow', 'whatsapp_flows')): ?>
                                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-flow' ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('company.modules.whatsapp-flow')); ?>" class="dash-link">
                                        <span class="dash-micon"><i class="ti ti-flow"></i></span><span
                                            class="dash-mtext"><?php echo e(__('WhatsApp Flow')); ?></span>
                                    </a>
                                </li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <!-- Templates - Show only if user has specific permission -->
                        <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('omx_flow')) || ($user->type === 'employee' && $userPlan->hasModule('omx_flow')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->omx_flow == 1))): ?>
                            <?php if($user->hasModulePermission('omx_flow', 'templates')): ?>
                                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-template' ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('company.modules.whatsapp-template')); ?>" class="dash-link">
                                        <span class="dash-micon"><i class="ti ti-message-circle"></i></span><span
                                            class="dash-mtext"><?php echo e(__('Templates')); ?></span>
                                    </a>
                                </li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <!-- Campaigns - Show only if user has specific permission -->
                        <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('omx_flow')) || ($user->type === 'employee' && $userPlan->hasModule('omx_flow')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->omx_flow == 1))): ?>
                            <?php if($user->hasModulePermission('omx_flow', 'campaigns')): ?>
                                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-campaign' ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('company.modules.whatsapp-campaign')); ?>" class="dash-link">
                                        <span class="dash-micon"><i class="ti ti-send"></i></span><span
                                            class="dash-mtext"><?php echo e(__('Campaigns')); ?></span>
                                    </a>
                                </li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <!-- WhatsApp Orders - Show only if user has specific permission -->
                        <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('omx_flow')) || ($user->type === 'employee' && $userPlan->hasModule('omx_flow')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->omx_flow == 1))): ?>
                            <?php if($user->hasModulePermission('omx_flow', 'whatsapp_orders')): ?>
                                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-orders' ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('company.modules.whatsapp-orders')); ?>" class="dash-link">
                                        <span class="dash-micon"><i class="ti ti-shopping-cart"></i></span><span
                                            class="dash-mtext"><?php echo e(__('WhatsApp Orders')); ?></span>
                                    </a>
                                </li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <!-- Chatbot - Show only if user has specific permission -->
                        <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('omx_flow')) || ($user->type === 'employee' && $userPlan->hasModule('omx_flow')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->omx_flow == 1))): ?>
                            <?php if($user->hasModulePermission('omx_flow', 'chatbot')): ?>
                                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'bot-replies' || Request::segment(3) == 'bot-flows') ? 'active dash-trigger' : ''); ?>">
                                    <a href="#!" class="dash-link">
                                        <span class="dash-micon"><i class="ti ti-robot"></i></span><span
                                            class="dash-mtext"><?php echo e(__('Chatbot')); ?></span>
                                        <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                                    </a>
                                    <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'bot-replies' || Request::segment(3) == 'bot-flows') ? 'show' : ''); ?>">
                                        <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'bot-replies' ? 'active' : ''); ?>">
                                            <a class="dash-link" href="<?php echo e(route('company.modules.bot-replies')); ?>">
                                                <?php echo e(__('All Chatbots')); ?>

                                            </a>
                                        </li>
                                        <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'bot-flows' ? 'active' : ''); ?>">
                                            <a class="dash-link" href="<?php echo e(route('company.modules.bot-flows')); ?>">
                                                <?php echo e(__('Flow Maker')); ?>

                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <!-- Unified Inbox - Available for all users (no restrictions) -->
                        <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('omx_flow')) || ($user->type === 'employee' && $userPlan->hasModule('omx_flow')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->omx_flow == 1))): ?>
                            <?php if(Gate::check('access omx flow')): ?>
                                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-chat' || Request::segment(3) == 'facebook-chat' || Request::segment(3) == 'instagram-chat') ? 'active dash-trigger' : ''); ?>">
                                    <a href="#!" class="dash-link">
                                        <span class="dash-micon"><i class="ti ti-inbox"></i></span><span
                                            class="dash-mtext"><?php echo e(__('Unified Inbox')); ?></span>
                                        <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                                    </a>
                                    <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-chat' || Request::segment(3) == 'facebook-chat' || Request::segment(3) == 'instagram-chat' || Request::segment(3) == 'whatsapp-api-setup' || Request::segment(3) == 'facebook-api-setup' || Request::segment(3) == 'instagram-api-setup') ? 'show' : ''); ?>">
                                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-chat' || Request::segment(3) == 'whatsapp-api-setup') ? 'active dash-trigger' : ''); ?>">
                                            <a class="dash-link" href="#!">
                                                <?php echo e(__('WhatsApp')); ?>

                                                <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                                            </a>
                                            <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-chat' || Request::segment(3) == 'whatsapp-api-setup') ? 'show' : ''); ?>">
                                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-chat' ? 'active' : ''); ?>">
                                                    <a class="dash-link" href="<?php echo e(route('company.modules.whatsapp-chat')); ?>">
                                                        <?php echo e(__('Chat')); ?>

                                                    </a>
                                                </li>
                                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-api-setup' ? 'active' : ''); ?>">
                                                    <a class="dash-link" href="<?php echo e(route('company.modules.whatsapp-api-setup')); ?>">
                                                        <?php echo e(__('API Setup')); ?>

                                                    </a>
                                                </li>
                                            </ul>
                                        </li>
                                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'facebook-chat' || Request::segment(3) == 'facebook-api-setup') ? 'active dash-trigger' : ''); ?>">
                                            <a class="dash-link" href="#!">
                                                <?php echo e(__('Facebook')); ?>

                                                <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                                            </a>
                                            <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'facebook-chat' || Request::segment(3) == 'facebook-api-setup') ? 'show' : ''); ?>">
                                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'facebook-chat' ? 'active' : ''); ?>">
                                                    <a class="dash-link" href="<?php echo e(route('company.modules.facebook-chat')); ?>">
                                                        <?php echo e(__('Chat')); ?>

                                                    </a>
                                                </li>
                                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'facebook-api-setup' ? 'active' : ''); ?>">
                                                    <a class="dash-link" href="<?php echo e(route('company.modules.facebook-api-setup')); ?>">
                                                        <?php echo e(__('API Setup')); ?>

                                                    </a>
                                                </li>
                                            </ul>
                                        </li>
                                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'instagram-chat' || Request::segment(3) == 'instagram-api-setup') ? 'active dash-trigger' : ''); ?>">
                                            <a class="dash-link" href="#!">
                                                <?php echo e(__('Instagram')); ?>

                                                <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                                            </a>
                                            <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'instagram-chat' || Request::segment(3) == 'instagram-api-setup') ? 'show' : ''); ?>">
                                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'instagram-chat' ? 'active' : ''); ?>">
                                                    <a class="dash-link" href="<?php echo e(route('company.modules.instagram-chat')); ?>">
                                                        <?php echo e(__('Chat')); ?>

                                                    </a>
                                                </li>
                                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'instagram-api-setup' ? 'active' : ''); ?>">
                                                    <a class="dash-link" href="<?php echo e(route('company.modules.instagram-api-setup')); ?>">
                                                        <?php echo e(__('API Setup')); ?>

                                                    </a>
                                                </li>
                                            </ul>
                                        </li>
                                    </ul>
                                </li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <!-- Automatish Module - Show for users with automatish module permission -->
                        <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('automatish')) || ($user->type === 'employee' && $userPlan->hasModule('automatish')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->automatish == 1))): ?>
                            <?php if(Gate::check('access automatish')): ?>
                                <?php
                                    $automatishModule = \App\Models\ModuleIntegration::where('name', 'Automatish')->where('enabled', true)->first();
                                ?>
                                <?php if($automatishModule && $automatishModule->sso_endpoint): ?>
                                    <li class="dash-item dash-hasmenu">
                                        <a href="<?php echo e(route('company.modules.automatish')); ?>" class="dash-link">
                                            <span class="dash-micon"><i class="ti ti-robot"></i></span><span
                                                class="dash-mtext"><?php echo e(__('Automatish')); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            <?php endif; ?>
                        <?php endif; ?>

                    <?php endif; ?>
                <?php endif; ?>

                <?php if(\Auth::user()->type == 'company'): ?>
                    <!-- <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'notification_templates' ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('notification-templates.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-notification"></i></span><span
                                class="dash-mtext"><?php echo e(__('Notification Template')); ?></span>
                        </a>
                    </li> -->
                <?php endif; ?>

                <!--------------------- Start System Setup ----------------------------------->

                <?php if(\Auth::user()->type != 'super admin' && \Auth::user()->type != 'system admin'): ?>
                    <?php if(Gate::check('manage company plan') || Gate::check('manage order') || Gate::check('manage company settings')): ?>
                        <li
                            class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'settings' ||
                            Request::segment(1) == 'plans' ||
                            Request::segment(1) == 'stripe' ||
                            Request::segment(1) == 'order'
                                ? ' active dash-trigger'
                                : ''); ?>">
                            <a href="<?php echo e(route('settings')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-cogs fa-2"></i></span><span
                                    class="dash-mtext"><?php echo e(__('System Settings')); ?></span>
                                <!-- <span class="dash-arrow">
                                    <i data-feather="chevron-right"></i>
                                </span> -->
                            </a>
                            <ul class="dash-submenu">
                                <?php if(Gate::check('manage company settings')): ?>
                                    <!-- <li
                                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'settings' ? ' active' : ''); ?>">
                                        <a href="<?php echo e(route('settings')); ?>"
                                            class="dash-link"><?php echo e(__('System Settings')); ?>

                                        </a>
                                    </li> -->
                                <?php endif; ?>
                                <?php if(Gate::check('manage pricing plan')): ?>
                                    <li
                                        class="dash-item<?php echo e(Request::route()->getName() == 'pricing-plans.index' || Request::route()->getName() == 'stripe' ? ' active' : ''); ?>">
                                        <a href="<?php echo e(route('pricing-plans.index')); ?>"
                                            class="dash-link"><?php echo e(__('Setup Subscription Plan')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <!-- <li
                                    class="dash-item<?php echo e(Request::route()->getName() == 'referral-program.company' ? ' active' : ''); ?>">
                                    <a href="<?php echo e(route('referral-program.company')); ?>"
                                        class="dash-link"><?php echo e(__('Referral Program')); ?></a>
                                </li> -->

                                <?php if(Gate::check('manage order') && Auth::user()->type == 'company'): ?>
                                    <!-- <li class="dash-item <?php echo e(Request::segment(1) == 'order' ? 'active' : ''); ?>">
                                        <a href="<?php echo e(route('order.index')); ?>" class="dash-link"><?php echo e(__('Order')); ?></a>
                                    </li> -->
                                <?php endif; ?>
                            </ul>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>
                <?php endif; ?>




                <!--------------------- Start Settings ----------------------------------->
                <?php if(\Auth::user()->type != 'super admin' && \Auth::user()->type != 'system admin' && \Auth::user()->type != 'staff'): ?>
                    <?php if(Request::segment(1) == 'settings'): ?>
                        <!-- Back to Main Menu -->
                        <li class="dash-item">
                            <a href="<?php echo e(route('dashboard')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-arrow-left"></i></span>
                                <span class="dash-mtext"><?php echo e(__('Back to Main Menu')); ?></span>
                            </a>
                        </li>
                        
                        <!-- Settings Sections -->
                        <li class="dash-item <?php echo e(!isset($section) || $section == 'brand' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('settings.brand')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-brand"></i></span>
                                <span class="dash-mtext"><?php echo e(__('Brand Settings')); ?></span>
                            </a>
                        </li>
                        <li class="dash-item <?php echo e($section == 'system' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('settings.system')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-device-desktop"></i></span>
                                <span class="dash-mtext"><?php echo e(__('General Settings')); ?></span>
                            </a>
                        </li>
                        <li class="dash-item <?php echo e($section == 'currency' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('settings.currency')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-currency-dollar"></i></span>
                                <span class="dash-mtext"><?php echo e(__('Currency Settings')); ?></span>
                            </a>
                        </li>
                        <li class="dash-item <?php echo e($section == 'email' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('settings.email')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-mail"></i></span>
                                <span class="dash-mtext"><?php echo e(__('Email Settings')); ?></span>
                            </a>
                        </li>
                        <li class="dash-item <?php echo e($section == 'sso' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('settings.sso')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-key"></i></span>
                                <span class="dash-mtext"><?php echo e(__('SSO Settings')); ?></span>
                            </a>
                        </li>
                        <li class="dash-item <?php echo e($section == 'custom-fields' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('settings.custom-fields')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-forms"></i></span>
                                <span class="dash-mtext"><?php echo e(__('Custom Field Settings')); ?></span>
                            </a>
                        </li>
                    <?php else: ?>
                        <!-- Main System Settings Link -->
                 
                    <?php endif; ?>
                <?php endif; ?>
                <!--------------------- End Settings ----------------------------------->

                <!--------------------- End System Setup ----------------------------------->
            </ul>
        <?php endif; ?>
        <?php if(\Auth::user()->type == 'client'): ?>
            <ul class="dash-navbar">
                <?php if(Gate::check('manage client dashboard')): ?>
                    <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'dashboard' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('client.dashboard.view')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-home fa-2"></i></span><span
                                class="dash-mtext"><?php echo e(__('Dashboard')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if(Gate::check('manage deal')): ?>
                    <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'deals' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('deals.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-rocket"></i></span><span
                                class="dash-mtext"><?php echo e(__('Deals')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if(Gate::check('manage contract')): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::route()->getName() == 'contract.index' || Request::route()->getName() == 'contract.show' ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('contract.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-rocket"></i></span><span
                                class="dash-mtext"><?php echo e(__('Contract')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if(Gate::check('manage project')): ?>
                    <li class="dash-item dash-hasmenu  <?php echo e(Request::segment(1) == 'projects' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('projects.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-share"></i></span><span
                                class="dash-mtext"><?php echo e(__('Project')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if(Gate::check('manage project')): ?>
                    <li
                        class="dash-item  <?php echo e(Request::route()->getName() == 'project_report.index' || Request::route()->getName() == 'project_report.show' ? 'active' : ''); ?>">
                        <a class="dash-link" href="<?php echo e(route('project_report.index')); ?>">
                            <span class="dash-micon"><i class="ti ti-chart-line"></i></span><span
                                class="dash-mtext"><?php echo e(__('Project Report')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if(Gate::check('manage project task')): ?>
                    <li class="dash-item dash-hasmenu  <?php echo e(Request::segment(1) == 'taskboard' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('taskBoard.view', 'list')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-list-check"></i></span><span
                                class="dash-mtext"><?php echo e(__('Tasks')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>



                
                

                <?php if(Gate::check('manage timesheet')): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'timesheet-list' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('timesheet.list')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-clock"></i></span><span
                                class="dash-mtext"><?php echo e(__('Timesheet')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>



                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'support' ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('support.index')); ?>" class="dash-link">
                        <span class="dash-micon"><i class="fas fa-headset fa-2"></i></span><span
                            class="dash-mtext"><?php echo e(__('Support System')); ?></span>
                    </a>
                </li>

                <!-- WhatsApp Template - Show only if user has OMX Flow permissions -->
                <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('omx_flow')) || ($user->type === 'employee' && $userPlan->hasModule('omx_flow')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->omx_flow == 1))): ?>
                    <?php if($user->hasModulePermission('omx_flow', 'templates') || Gate::check('access omx flow')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-template' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('company.modules.whatsapp-template')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-message-circle"></i></span><span
                                    class="dash-mtext"><?php echo e(__('WhatsApp Template')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- WhatsApp Campaign - Show only if user has OMX Flow permissions -->
                <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('omx_flow')) || ($user->type === 'employee' && $userPlan->hasModule('omx_flow')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->omx_flow == 1))): ?>
                    <?php if($user->hasModulePermission('omx_flow', 'campaigns') || Gate::check('access omx flow')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-campaign' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('company.modules.whatsapp-campaign')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-send"></i></span><span
                                    class="dash-mtext"><?php echo e(__('WhatsApp Campaign')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- WhatsApp Orders - Show only if user has OMX Flow permissions -->
                <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('omx_flow')) || ($user->type === 'employee' && $userPlan->hasModule('omx_flow')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->omx_flow == 1))): ?>
                    <?php if($user->hasModulePermission('omx_flow', 'whatsapp_orders') || Gate::check('access omx flow')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-orders' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('company.modules.whatsapp-orders')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-shopping-cart"></i></span><span
                                    class="dash-mtext"><?php echo e(__('WhatsApp Orders')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- WhatsApp Flow - Show only if user has OMX Flow permissions -->
                <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('omx_flow')) || ($user->type === 'employee' && $userPlan->hasModule('omx_flow')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->omx_flow == 1))): ?>
                    <?php if($user->hasModulePermission('omx_flow', 'whatsapp_flows') || Gate::check('access omx flow')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-flow' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('company.modules.whatsapp-flow')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-flow"></i></span><span
                                    class="dash-mtext"><?php echo e(__('WhatsApp Flow')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>
            </ul>
        <?php endif; ?>
                <?php if(\Auth::user()->type == 'system admin' || \Auth::user()->type == 'staff'): ?>
            <ul class="dash-navbar">
                <!-- Dashboard - Show only if user has permission or is system admin -->
                <?php if(\Auth::user()->type == 'system admin' || \Auth::user()->can('view system admin dashboard')): ?>
                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'system-admin' && Request::segment(2) == 'dashboard' ? ' active' : ''); ?>">
                    <a href="<?php echo e(route('system-admin.dashboard')); ?>" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-home"></i></span><span
                            class="dash-mtext"><?php echo e(__('Dashboard')); ?></span>
                    </a>
                </li>
                <?php endif; ?>

                <!-- White Label - Show if user has permission or is system admin -->
                <?php if(\Auth::user()->type == 'system admin' || \Auth::user()->can('view white label')): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::route()->getName() == 'users.index' || Request::route()->getName() == 'users.create' || Request::route()->getName() == 'users.edit' || Request::route()->getName() == 'user.userlog' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('users.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-users"></i></span><span
                                class="dash-mtext"><?php echo e(__('White Label')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <!-- Sub-accounts - Show if user has permission or is system admin -->
                <?php if(\Auth::user()->type == 'system admin' || \Auth::user()->can('view sub accounts')): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'system-admin' && Request::segment(2) == 'companies' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('system-admin.companies')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-building"></i></span><span
                                class="dash-mtext"><?php echo e(__('Sub-accounts')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <!-- Staff Management - Only for System Admins -->
                <?php if(\Auth::user()->type == 'system admin'): ?>
                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'system-admin' && Request::segment(2) == 'staff' ? ' active' : ''); ?>">
                    <a href="<?php echo e(route('system-admin.staff.index')); ?>" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-user-plus"></i></span><span
                            class="dash-mtext"><?php echo e(__('Staff Management')); ?></span>
                    </a>
                </li>
                <?php endif; ?>

                <!-- Role Management - Hidden from system admin, only show for other user types with permission -->
                <?php if(\Auth::user()->type != 'system admin' && \Auth::user()->can('view role management')): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::route()->getName() == 'roles.index' || Request::route()->getName() == 'roles.create' || Request::route()->getName() == 'roles.edit' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('roles.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-shield"></i></span><span
                                class="dash-mtext"><?php echo e(__('Role Management')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                
                <!-- Plan Management - Show if user has permission or is system admin -->
                

                <!-- Pricing Plans - Show if user has permission or is system admin -->
                <?php if(\Auth::user()->type == 'system admin' || \Auth::user()->can('view pricing plans')): ?>
                    <li class="dash-item dash-hasmenu  <?php echo e(Request::segment(1) == 'pricing-plans' ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('pricing-plans.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-currency-dollar"></i></span><span
                                class="dash-mtext"><?php echo e(__('Pricing Plans')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <!-- Support System - Show to system admin or staff with permission -->
                <?php if(\Auth::user()->type == 'system admin' || (\Auth::user()->type == 'staff' && \Auth::user()->can('view support system'))): ?>
                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'support' ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('support.index')); ?>" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-headphones"></i></span><span
                            class="dash-mtext"><?php echo e(__('Support System')); ?></span>
                    </a>
                </li>
                <?php endif; ?>

                <!-- Settings - Show only to system admin -->
                <?php if(\Auth::user()->type == 'system admin'): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::route()->getName() == 'systems.index' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('systems.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-settings"></i></span><span
                                class="dash-mtext"><?php echo e(__('Settings')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <!-- WhatsApp Template - Available for all users (no restrictions) -->
                <!-- <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-template' || Request::segment(3) == 'whatsapp-campaign') ? 'active dash-trigger' : ''); ?>">
                    <a href="#!" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-message-circle"></i></span><span
                            class="dash-mtext"><?php echo e(__('WhatsApp Template')); ?></span>
                        <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                    </a>
                    <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-template' || Request::segment(3) == 'whatsapp-campaign') ? 'show' : ''); ?>">
                        <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-template' ? 'active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('company.modules.whatsapp-template')); ?>">
                                <?php echo e(__('Templates')); ?>

                            </a>
                        </li>
                        <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-campaign' ? 'active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('company.modules.whatsapp-campaign')); ?>">
                                <?php echo e(__('Campaign')); ?>

                            </a>
                        </li>
                    </ul>
                </li> -->

                <!-- WhatsApp Flow - Available for all users (no restrictions) -->
                <!-- <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-flow' ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('company.modules.whatsapp-flow')); ?>" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-flow"></i></span><span
                            class="dash-mtext"><?php echo e(__('WhatsApp Flow')); ?></span>
                    </a>
                </li> -->

                <!-- Bot Management - Available for all users (no restrictions) -->
                <!-- <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'bot-replies' || Request::segment(3) == 'bot-flows') ? 'active dash-trigger' : ''); ?>">
                    <a href="#!" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-robot"></i></span><span
                            class="dash-mtext"><?php echo e(__('Bot Management')); ?></span>
                        <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                    </a>
                    <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'bot-replies' || Request::segment(3) == 'bot-flows') ? 'show' : ''); ?>">
                        <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'bot-replies' ? 'active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('company.modules.bot-replies')); ?>">
                                <?php echo e(__('Bot Replies')); ?>

                            </a>
                        </li>
                        <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'bot-flows' ? 'active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('company.modules.bot-flows')); ?>">
                                <?php echo e(__('Bot Flows')); ?>

                            </a>
                        </li>
                    </ul>
                </li> -->
            </ul>
        <?php endif; ?>

        <?php if(\Auth::user()->type == 'super admin'): ?>
            <ul class="dash-navbar">
                <?php if(Gate::check('manage super admin dashboard')): ?>
                    <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'dashboard' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('client.dashboard.view')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-home"></i></span><span
                                class="dash-mtext"><?php echo e(__('Dashboard')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage user')): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::route()->getName() == 'users.index' || Request::route()->getName() == 'users.create' || Request::route()->getName() == 'users.edit' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('users.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-users"></i></span><span
                                class="dash-mtext"><?php echo e(__('Companies')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if(Gate::check('manage pricing plan')): ?>
                    <li class="dash-item dash-hasmenu  <?php echo e(Request::segment(1) == 'pricing-plans' ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('pricing-plans.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-currency-dollar"></i></span><span
                                class="dash-mtext"><?php echo e(__('Pricing Plans')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <li class="dash-item dash-hasmenu <?php echo e(request()->is('plan_request*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('plan_request.index')); ?>" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-arrow-up-right-circle"></i></span><span
                            class="dash-mtext"><?php echo e(__('Plan Request')); ?></span>
                    </a>
                </li>

                <li class="dash-item dash-hasmenu  <?php echo e(Request::segment(1) == '' ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('referral-program.index')); ?>" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-discount-2"></i></span><span
                            class="dash-mtext"><?php echo e(__('Referral Program')); ?></span>
                    </a>
                </li>

                <?php if(Gate::check('manage coupon')): ?>
                    <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'coupons' ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('coupons.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-gift"></i></span><span
                                class="dash-mtext"><?php echo e(__('Coupon')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if(Gate::check('manage order')): ?>
                    <li class="dash-item dash-hasmenu  <?php echo e(Request::segment(1) == 'orders' ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('order.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-shopping-cart-plus"></i></span><span
                                class="dash-mtext"><?php echo e(__('Order')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <li
                    class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'email_template' || Request::route()->getName() == 'manage.email.language' ? ' active dash-trigger' : 'collapsed'); ?>">
                    <a href="<?php echo e(route('email_template.index')); ?>" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-template"></i></span>
                        <span class="dash-mtext"><?php echo e(__('Email Template')); ?></span>
                    </a>
                </li>

                <?php echo $__env->make('landingpage::menu.landingpage', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                <!-- OMX Flow for Super Admin - Show for users with omx flow module permission -->
                <?php if(!empty($userPlan) && (($user->type === 'super admin' && $userPlan->hasModule('omx_flow')) || ($user->type === 'system admin'))): ?>
                    <?php if(Gate::check('access omx flow')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('company.modules.omx-flow')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-rocket"></i></span><span
                                    class="dash-mtext"><?php echo e(__('OMX Flow')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- WhatsApp Template for Super Admin - Show only if user has OMX Flow permissions -->
                <?php if(!empty($userPlan) && (($user->type === 'super admin' && $userPlan->hasModule('omx_flow')) || ($user->type === 'system admin'))): ?>
                    <?php if(Gate::check('access omx flow')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-template' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('company.modules.whatsapp-template')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-message-circle"></i></span><span
                                    class="dash-mtext"><?php echo e(__('WhatsApp Template')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- WhatsApp Campaign for Super Admin - Show only if user has OMX Flow permissions -->
                <?php if(!empty($userPlan) && (($user->type === 'super admin' && $userPlan->hasModule('omx_flow')) || ($user->type === 'system admin'))): ?>
                    <?php if(Gate::check('access omx flow')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-campaign' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('company.modules.whatsapp-campaign')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-send"></i></span><span
                                    class="dash-mtext"><?php echo e(__('WhatsApp Campaign')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- WhatsApp Orders for Super Admin - Show only if user has OMX Flow permissions -->
                <?php if(!empty($userPlan) && (($user->type === 'super admin' && $userPlan->hasModule('omx_flow')) || ($user->type === 'system admin'))): ?>
                    <?php if(Gate::check('access omx flow')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-orders' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('company.modules.whatsapp-orders')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-shopping-cart"></i></span><span
                                    class="dash-mtext"><?php echo e(__('WhatsApp Orders')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- WhatsApp Flow for Super Admin - Show only if user has OMX Flow permissions -->
                <?php if(!empty($userPlan) && (($user->type === 'super admin' && $userPlan->hasModule('omx_flow')) || ($user->type === 'system admin'))): ?>
                    <?php if(Gate::check('access omx flow')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-flow' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('company.modules.whatsapp-flow')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-flow"></i></span><span
                                    class="dash-mtext"><?php echo e(__('WhatsApp Flow')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- ChatBot Management for Super Admin - Show only if user has OMX Flow permissions -->
                <?php if(!empty($userPlan) && (($user->type === 'super admin' && $userPlan->hasModule('omx_flow')) || ($user->type === 'system admin'))): ?>
                    <?php if(Gate::check('access omx flow')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'bot-replies' || Request::segment(3) == 'bot-flows') ? 'active dash-trigger' : ''); ?>">
                            <a href="#!" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-robot"></i></span><span
                                    class="dash-mtext"><?php echo e(__('Chatbot')); ?></span>
                                <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'bot-replies' || Request::segment(3) == 'bot-flows') ? 'show' : ''); ?>">
                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'bot-replies' ? 'active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('company.modules.bot-replies')); ?>">
                                        <?php echo e(__('All Chatbots')); ?>

                                    </a>
                                </li>
                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'bot-flows' ? 'active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('company.modules.bot-flows')); ?>">
                                        <?php echo e(__('Flow Maker')); ?>

                                    </a>
                                </li>
                            </ul>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- Bot Management for Super Admin - Available for all users (no restrictions) -->
                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'bot-replies' || Request::segment(3) == 'bot-flows') ? 'active dash-trigger' : ''); ?>">
                    <a href="#!" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-robot"></i></span><span
                            class="dash-mtext"><?php echo e(__('Bot Management')); ?></span>
                        <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                    </a>
                    <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'bot-replies' || Request::segment(3) == 'bot-flows') ? 'show' : ''); ?>">
                        <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'bot-replies' ? 'active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('company.modules.bot-replies')); ?>">
                                <?php echo e(__('Bot Replies')); ?>

                            </a>
                        </li>
                        <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'bot-flows' ? 'active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('company.modules.bot-flows')); ?>">
                                <?php echo e(__('Bot Flows')); ?>

                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Unified Inbox for Super Admin - Available for all users (no restrictions) -->
                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-chat' || Request::segment(3) == 'facebook-chat' || Request::segment(3) == 'instagram-chat') ? 'active dash-trigger' : ''); ?>">
                    <a href="#!" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-inbox"></i></span><span
                            class="dash-mtext"><?php echo e(__('Unified Inbox')); ?></span>
                        <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                    </a>
                    <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-chat' || Request::segment(3) == 'facebook-chat' || Request::segment(3) == 'instagram-chat' || Request::segment(3) == 'whatsapp-api-setup' || Request::segment(3) == 'facebook-api-setup' || Request::segment(3) == 'instagram-api-setup') ? 'show' : ''); ?>">
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-chat' || Request::segment(3) == 'whatsapp-api-setup') ? 'active dash-trigger' : ''); ?>">
                            <a class="dash-link" href="#!">
                                <?php echo e(__('WhatsApp')); ?>

                                <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-chat' || Request::segment(3) == 'whatsapp-api-setup') ? 'show' : ''); ?>">
                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-chat' ? 'active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('company.modules.whatsapp-chat')); ?>">
                                        <?php echo e(__('Chat')); ?>

                                    </a>
                                </li>
                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-api-setup' ? 'active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('company.modules.whatsapp-api-setup')); ?>">
                                        <?php echo e(__('API Setup')); ?>

                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'facebook-chat' || Request::segment(3) == 'facebook-api-setup') ? 'active dash-trigger' : ''); ?>">
                            <a class="dash-link" href="#!">
                                <?php echo e(__('Facebook')); ?>

                                <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'facebook-chat' || Request::segment(3) == 'facebook-api-setup') ? 'show' : ''); ?>">
                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'facebook-chat' ? 'active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('company.modules.facebook-chat')); ?>">
                                        <?php echo e(__('Chat')); ?>

                                    </a>
                                </li>
                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'facebook-api-setup' ? 'active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('company.modules.facebook-api-setup')); ?>">
                                        <?php echo e(__('API Setup')); ?>

                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'instagram-chat' || Request::segment(3) == 'instagram-api-setup') ? 'active dash-trigger' : ''); ?>">
                            <a class="dash-link" href="#!">
                                <?php echo e(__('Instagram')); ?>

                                <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'instagram-chat' || Request::segment(3) == 'instagram-api-setup') ? 'show' : ''); ?>">
                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'instagram-chat' ? 'active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('company.modules.instagram-chat')); ?>">
                                        <?php echo e(__('Chat')); ?>

                                    </a>
                                </li>
                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'instagram-api-setup' ? 'active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('company.modules.instagram-api-setup')); ?>">
                                        <?php echo e(__('API Setup')); ?>

                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </li>

                <!-- Automatish Module - Show for super admin users with automatish module permission -->
                <?php if(!empty($userPlan) && (($user->type === 'super admin' && $userPlan->hasModule('automatish')) || ($user->type === 'system admin'))): ?>
                    <?php if(Gate::check('access automatish')): ?>
                        <?php
                            $automatishModule = \App\Models\ModuleIntegration::where('name', 'Automatish')->where('enabled', true)->first();
                        ?>
                        <?php if($automatishModule && $automatishModule->sso_endpoint): ?>
                            <li class="dash-item dash-hasmenu">
                                <?php if(Auth::user()->type === 'company'): ?>
                                    <a href="<?php echo e(route('company.modules.automatish')); ?>" class="dash-link">
                                <?php else: ?>
                                    <a href="<?php echo e(route('module-integration.sso-login', $automatishModule->id)); ?>" class="dash-link">
                                <?php endif; ?>
                                    <span class="dash-micon"><i class="ti ti-robot"></i></span><span
                                        class="dash-mtext"><?php echo e(__('Automatish')); ?></span>
                                </a>
                            </li>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php endif; ?>

                <?php if(Gate::check('manage system settings')): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::route()->getName() == 'systems.index' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('systems.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-settings"></i></span><span
                                class="dash-mtext"><?php echo e(__('Settings')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

            </ul>
        <?php endif; ?>


    </div>
</div>
</nav>

<?php /**PATH C:\Users\<USER>\Desktop\new_laravel_project\omx-new-saas\resources\views/partials/admin/menu.blade.php ENDPATH**/ ?>